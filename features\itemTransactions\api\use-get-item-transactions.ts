import { useQuery } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/auth-client";

export const useGetItemTransactions = (params?: {
  from?: string;
  to?: string;
  accountId?: string;
  itemId?: string;
  categoryId?: string;
}) => {
  const userId = useCurrentUserId();

  const query = useQuery({
    enabled: !!userId,
    queryKey: ["item-transactions", { userId, ...params }],
    queryFn: async () => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.itemTransactions.$get(
        {
          query: {
            from: params?.from || "",
            to: params?.to || "",
            accountId: params?.accountId || "",
            itemId: params?.itemId || "",
            categoryId: params?.categoryId || "",
          },
        },
        {
          headers: {
            "X-User-ID": userId,
          },
        },
      );

      if (!response.ok) {
        throw new Error("Failed to fetch item transactions");
      }

      const { data } = await response.json();
      return data;
    },
  });

  return query;
};