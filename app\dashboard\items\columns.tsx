"use client";

import { ColumnDef } from "@tanstack/react-table";
import { InferResponseType } from "hono";
import { client } from "@/lib/hono";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { ArrowUpDown, Package, Tag, Store } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { ItemActions } from "./item-actions";

export type ResponseType = InferResponseType<
  typeof client.api.items.$get,
  200
>["data"][0];

export const itemColumns: ColumnDef<ResponseType>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableHiding: false,
    enableSorting: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        <Package className="mr-2 h-4 w-4" />
        Item Name
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const name: string = row.getValue("name");
      const description: string | null = row.original.description;

      return (
        <div>
          <div className="font-medium">{name}</div>
          {description && (
            <div className="text-sm text-muted-foreground line-clamp-1">
              {description}
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "payee",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        <Store className="mr-2 h-4 w-4" />
        Store/Vendor
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const payee: string | null = row.original.payee;
      
      return payee ? (
        <Badge variant="secondary">{payee}</Badge>
      ) : (
        <span className="text-muted-foreground">No store</span>
      );
    },
  },
  {
    accessorKey: "defaultCategoryName",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        <Tag className="mr-2 h-4 w-4" />
        Default Category
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const categoryName: string | null = row.getValue("defaultCategoryName");
      
      return categoryName ? (
        <Badge variant="outline">{categoryName}</Badge>
      ) : (
        <span className="text-muted-foreground">No category</span>
      );
    },
  },
  {
    accessorKey: "usageCount",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Usage Count
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const usageCount: number = row.getValue("usageCount") || 0;
      
      return (
        <Badge 
          variant={usageCount > 0 ? "default" : "secondary"}
          className="text-xs font-medium px-2 py-1"
        >
          {usageCount} transaction{usageCount !== 1 ? 's' : ''}
        </Badge>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Created
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }) => {
      const date = row.getValue("createdAt") as Date;

      return (
        <div className="text-sm">
          {format(new Date(date), "dd MMM yyyy")}
        </div>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => (
      <ItemActions 
        id={row.original.id} 
        name={row.original.name}
        payee={row.original.payee}
        usageCount={row.original.usageCount || 0}
      />
    ),
  },
];