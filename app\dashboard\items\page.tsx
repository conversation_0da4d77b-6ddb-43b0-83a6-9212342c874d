"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { useState } from "react";
import { Loader2, Package, Plus, RefreshCcw, Search } from "lucide-react";
import { DataTable } from "@/components/data-table";
import { createId } from "@paralleldrive/cuid2";
import { itemColumns } from "./columns";
import { useGetItems } from "@/features/items/api/use-get-items";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useGetCategoriesAll } from "@/features/categories/api/use-get-categories-all";
import { useNewItem } from "@/features/items/hooks/use-new-item";
import { useOpenItem } from "@/features/items/hooks/use-open-item";
import { NewItemDialog } from "@/features/items/components/new-item-dialog";
import { EditItemDialog } from "@/features/items/components/edit-item-dialog";

const Page = () => {
  const [renderKey, setRenderKey] = useState(createId());
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  
  const itemsQuery = useGetItems({
    search: searchQuery || undefined,
    categoryId: selectedCategory === "all" ? undefined : selectedCategory,
  });
  
  const categoriesQuery = useGetCategoriesAll();
  const items = itemsQuery.data || [];
  const categories = categoriesQuery.data || [];
  
  const isDisabled = itemsQuery.isLoading;

  const rerender = () => {
    setRenderKey(createId());
  };

  const { onOpen: openNewItem } = useNewItem();
  const { isOpen: isEditOpen, id: editId, onClose: closeEditItem } = useOpenItem();
  const { isOpen: isNewOpen, onClose: closeNewItem } = useNewItem();
  
  const handleAddItem = () => {
    openNewItem();
  };

  if (itemsQuery.isLoading) {
    return (
      <div className="max-w-screen-2xl mx-auto w-full pb-10">
        <Card className="border-none drop-shadow-sm">
          <CardHeader>
            <Skeleton className="h-8 w-48" />
          </CardHeader>
          <CardContent>
            <div className="h-[500px] flex items-center justify-center">
              <Loader2 className="size-4 text-slate-300 animate-spin" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-screen-2xl mx-auto w-full pb-10">
      <Card key={renderKey} className="border-none drop-shadow-sm">
        <CardHeader>
          <CardTitle className="line-clamp-1 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Items Management
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={rerender} size="sm">
                <RefreshCcw className="h-4 w-4" />
              </Button>
              <Button onClick={handleAddItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex items-center gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search items by name..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Items Table */}
          <DataTable
            filterKey="name"
            columns={itemColumns}
            data={items}
            disabled={isDisabled}
            onDelete={(rows) => {
              const ids = rows.map((row) => row.original.id);
              // TODO: Implement bulk delete
              console.log("Delete items:", ids);
            }}
          />

          {/* Summary */}
          {items.length > 0 && (
            <div className="mt-4 flex items-center justify-between text-sm text-muted-foreground">
              <div>
                Total: {items.length} item{items.length !== 1 ? 's' : ''}
              </div>
              <div>
                Used in transactions: {items.filter(item => item.usageCount && item.usageCount > 0).length}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      <NewItemDialog 
        isOpen={isNewOpen}
        onClose={closeNewItem}
      />
      
      <EditItemDialog
        isOpen={isEditOpen}
        onClose={closeEditItem}
        id={editId}
      />
    </div>
  );
};

export default Page;