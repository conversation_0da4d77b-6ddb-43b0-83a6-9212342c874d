import { z } from "zod";

import { <PERSON>listForm } from "./wishlist-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useEditWishlistItem } from "../hooks/use-edit-wishlist-item";
import { useEditWishlistItem as useEditWishlistItemApi } from "../api/use-edit-wishlist-item";
import { useEditWishlistItemWithFiles } from "../api/use-edit-wishlist-item-with-files";
import { useDeleteWishlistItem } from "../api/use-delete-wishlist-item";
import { useGetWishlistItem } from "../api/use-get-wishlist-item";
import { Loader2 } from "lucide-react";
import { convertAmountFormMiliunits } from "@/lib/utils";

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  category: z.string().min(1, "Category is required"),
  estimatedCost: z.number(),
  targetAmount: z.number().optional(),
  targetDate: z.coerce.date().optional(),
  priority: z.string().default("medium"),
  status: z.string().default("planned"),
  notes: z.string().optional(),
  links: z.string().optional(),
  imageUrl: z.string().optional(),
  motivation: z.string().optional(),
});

type FormValues = z.input<typeof formSchema>;

export const EditWishlistItemDialog = () => {
  const { isOpen, onClose, id } = useEditWishlistItem();
  const editMutation = useEditWishlistItemApi(id);
  const editWithFilesMutation = useEditWishlistItemWithFiles(id);
  const deleteMutation = useDeleteWishlistItem(id);
  const wishlistItemQuery = useGetWishlistItem(id);

  const isPending = editMutation.isPending || editWithFilesMutation.isPending || deleteMutation.isPending;
  const isLoading = wishlistItemQuery.isLoading;

  const onSubmit = (values: FormValues, files?: FileList) => {
    if (files && files.length > 0) {
      editWithFilesMutation.mutate({ data: values, files }, {
        onSuccess: () => {
          onClose();
        },
      });
    } else {
      editMutation.mutate(values, {
        onSuccess: () => {
          onClose();
        },
      });
    }
  };

  const onDelete = () => {
    deleteMutation.mutate(undefined, {
      onSuccess: () => {
        onClose();
      },
    });
  };

  const defaultValues = wishlistItemQuery.data
    ? {
        name: wishlistItemQuery.data.name,
        category: wishlistItemQuery.data.category,
        estimatedCost: convertAmountFormMiliunits(wishlistItemQuery.data.estimatedCost).toString(),
        targetAmount: wishlistItemQuery.data.targetAmount
          ? convertAmountFormMiliunits(wishlistItemQuery.data.targetAmount).toString()
          : "",
        targetDate: wishlistItemQuery.data.targetDate
          ? new Date(wishlistItemQuery.data.targetDate)
          : undefined,
        priority: wishlistItemQuery.data.priority,
        status: wishlistItemQuery.data.status,
        notes: wishlistItemQuery.data.notes || "",
        links: wishlistItemQuery.data.links || "",
        imageUrl: wishlistItemQuery.data.imageUrl || "",
        motivation: wishlistItemQuery.data.motivation || "",
      }
    : undefined;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Wishlist Item</DialogTitle>
          <DialogDescription>
            Update your wishlist item details
          </DialogDescription>
        </DialogHeader>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="size-4 text-muted-foreground animate-spin" />
          </div>
        ) : (
          <WishlistForm
            id={id}
            defaultValues={defaultValues}
            onSubmit={onSubmit}
            onDelete={onDelete}
            disabled={isPending}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};