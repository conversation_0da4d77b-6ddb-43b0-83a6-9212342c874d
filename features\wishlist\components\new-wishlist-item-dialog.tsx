import { z } from "zod";

import { WishlistForm } from "./wishlist-form";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useNewWishlistItem } from "../hooks/use-new-wishlist-item";
import { useCreateWishlistItem } from "../api/use-create-wishlist-item";
import { useCreateWishlistWithFiles } from "../api/use-create-wishlist-with-files";

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  category: z.string().min(1, "Category is required"),
  estimatedCost: z.number(),
  targetAmount: z.number().optional(),
  targetDate: z.coerce.date().optional(),
  priority: z.string().default("medium"),
  status: z.string().default("planned"),
  notes: z.string().optional(),
  links: z.string().optional(),
  imageUrl: z.string().optional(),
  motivation: z.string().optional(),
});

type FormValues = z.input<typeof formSchema>;

export const NewWishlistItemDialog = () => {
  const { isOpen, onClose } = useNewWishlistItem();
  const createMutation = useCreateWishlistItem();
  const createWithFilesMutation = useCreateWishlistWithFiles();

  const onSubmit = (values: FormValues, files?: FileList) => {
    if (files && files.length > 0) {
      createWithFilesMutation.mutate({ data: values, files }, {
        onSuccess: () => {
          onClose();
        },
      });
    } else {
      createMutation.mutate(values, {
        onSuccess: () => {
          onClose();
        },
      });
    }
  };

  const isPending = createMutation.isPending || createWithFilesMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Wish</DialogTitle>
          <DialogDescription>
            Add something you want to buy to your wishlist
          </DialogDescription>
        </DialogHeader>
        <WishlistForm onSubmit={onSubmit} disabled={isPending} />
      </DialogContent>
    </Dialog>
  );
};