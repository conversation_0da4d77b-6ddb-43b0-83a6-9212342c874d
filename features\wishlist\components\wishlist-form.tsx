import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { useState } from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/date-picker";
import { convertAmountToMiliunits } from "@/lib/utils";
import { FileInput } from "@/components/file-input";
import { MediaGallery } from "@/components/media-gallery";
import { Image } from "lucide-react";

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  category: z.string().min(1, "Category is required"),
  estimatedCost: z.string().min(1, "Estimated cost is required"),
  targetAmount: z.string().optional(),
  quantity: z.string().min(1, "Quantity is required").default("1"),
  targetDate: z.coerce.date().optional(),
  priority: z.string().default("medium"),
  status: z.string().default("planned"),
  notes: z.string().optional(),
  links: z.string().optional(),
  imageUrl: z.string().optional(),
  motivation: z.string().optional(),
});

type FormValues = z.input<typeof formSchema>;

type Props = {
  id?: string;
  defaultValues?: FormValues;
  onSubmit: (values: FormValues, files?: FileList) => void;
  onDelete?: () => void;
  disabled?: boolean;
};

const categories = [
  "Electronics",
  "Travel",
  "Clothing",
  "Experiences",
  "Education",
  "Home",
  "Health",
  "Books",
  "Sports",
  "Other",
];

const priorities = [
  { value: "high", label: "High" },
  { value: "medium", label: "Medium" },
  { value: "low", label: "Low" },
];

const statuses = [
  { value: "planned", label: "Planned" },
  { value: "in_progress", label: "In Progress" },
  { value: "on_hold", label: "On Hold" },
  { value: "achieved", label: "Achieved" },
];

export const WishlistForm = ({
  id,
  defaultValues,
  onSubmit,
  onDelete,
  disabled,
}: Props) => {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultValues || {
      name: "",
      category: "",
      estimatedCost: "",
      targetAmount: "",
      quantity: "1",
      priority: "medium",
      status: "planned",
      notes: "",
      links: "",
      imageUrl: "",
      motivation: "",
    },
  });

  const handleSubmit = (values: FormValues) => {
    const processedValues = {
      ...values,
      estimatedCost: convertAmountToMiliunits(parseFloat(values.estimatedCost)),
      targetAmount: values.targetAmount 
        ? convertAmountToMiliunits(parseFloat(values.targetAmount))
        : undefined,
      quantity: parseInt(values.quantity),
    };
    
    // Convert File[] to FileList
    const fileList = uploadedFiles.length > 0 ? (() => {
      const dt = new DataTransfer();
      uploadedFiles.forEach(file => dt.items.add(file));
      return dt.files;
    })() : undefined;
    
    onSubmit(processedValues, fileList);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input
                  disabled={disabled}
                  placeholder="Nintendo Switch"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <Select
                  disabled={disabled}
                  onValueChange={field.onChange}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="estimatedCost"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Estimated Cost</FormLabel>
                <FormControl>
                  <Input
                    disabled={disabled}
                    placeholder="299.99"
                    type="number"
                    step="0.01"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="quantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quantity</FormLabel>
                <FormControl>
                  <Input
                    disabled={disabled}
                    placeholder="1"
                    type="number"
                    min="1"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="targetAmount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Target Amount (Optional)</FormLabel>
                <FormControl>
                  <Input
                    disabled={disabled}
                    placeholder="350.00"
                    type="number"
                    step="0.01"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="targetDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Target Date (Optional)</FormLabel>
                <FormControl>
                  <DatePicker
                    value={field.value}
                    onChange={field.onChange}
                    disabled={disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="priority"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Priority</FormLabel>
                <Select
                  disabled={disabled}
                  onValueChange={field.onChange}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {priorities.map((priority) => (
                      <SelectItem key={priority.value} value={priority.value}>
                        {priority.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select
                  disabled={disabled}
                  onValueChange={field.onChange}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {statuses.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="imageUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Image URL (Optional)</FormLabel>
              <FormControl>
                <Input
                  disabled={disabled}
                  placeholder="https://example.com/image.jpg"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="links"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Links (Optional)</FormLabel>
              <FormControl>
                <Input
                  disabled={disabled}
                  placeholder="https://store.example.com/product"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="motivation"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Motivation (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  disabled={disabled}
                  placeholder="Why do you want this?"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  disabled={disabled}
                  placeholder="Additional details, model, brand, etc."
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Separator className="my-4" />
        <div className="space-y-4">
          <FormLabel className="flex items-center gap-2">
            <Image className="h-4 w-4" />
            Item Images & Media
          </FormLabel>
          
          {id ? (
            // Edit mode: Show existing files and allow new uploads
            <div className="space-y-4">
              <MediaGallery
                entityType="wishlist"
                entityId={id}
                category="images"
                allowDelete={true}
                showViewToggle={false}
                compact={false}
              />
              <div className="border-t pt-4">
                <FileInput
                  onFilesSelected={setUploadedFiles}
                  maxFiles={15}
                  acceptedTypes={['image/jpeg', 'image/png', 'image/webp', 'image/gif']}
                  allowCamera={true}
                  title="Add More Images"
                  description="Select additional product photos and inspiration images to upload"
                />
              </div>
            </div>
          ) : (
            // Create mode: Allow file selection for upload after creation
            <FileInput
              onFilesSelected={setUploadedFiles}
              maxFiles={15}
              acceptedTypes={['image/jpeg', 'image/png', 'image/webp', 'image/gif']}
              allowCamera={true}
              title="Select Item Images"
              description="Choose product photos and inspiration images to upload with this item"
            />
          )}
        </div>

        <div className="flex gap-2">
          <Button type="submit" disabled={disabled} className="flex-1">
            {id ? "Update" : "Create"} Wishlist Item
          </Button>
          {!!id && (
            <Button
              type="button"
              disabled={disabled}
              onClick={onDelete}
              variant="destructive"
            >
              Delete
            </Button>
          )}
        </div>
      </form>
    </Form>
  );
};