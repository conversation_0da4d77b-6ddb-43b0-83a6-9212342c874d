import { useMutation, useQueryClient } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import { toast } from "sonner";

import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/auth-client";

type ResponseType = InferResponseType<typeof client.api.items[":id"]["$patch"]>;
type RequestType = InferRequestType<typeof client.api.items[":id"]["$patch"]>["json"];

export const useEditItem = (id?: string) => {
  const queryClient = useQueryClient();
  const userId = useCurrentUserId();

  const mutation = useMutation<ResponseType, Error, RequestType>({
    mutationFn: async (json) => {
      const response = await client.api.items[":id"]["$patch"](
        { param: { id }, json },
        { headers: { "X-User-ID": userId! } }
      );
      return await response.json();
    },
    onSuccess: () => {
      toast.success("Item updated");
      queryClient.invalidateQueries({ queryKey: ["items"] });
      queryClient.invalidateQueries({ queryKey: ["item", { id }] });
      queryClient.invalidateQueries({ queryKey: ["item-transactions"] });
    },
    onError: () => {
      toast.error("Failed to update item");
    },
  });

  return mutation;
};