"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Edit, Eye, MoreHorizontal, Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

type Props = {
  id: string;
  name: string;
  payee?: string | null;
  usageCount: number;
};

export const ItemActions = ({ id, name, payee, usageCount }: Props) => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);

  const handleView = () => {
    router.push(`/dashboard/items/${id}`);
    setIsOpen(false);
  };

  const handleEdit = () => {
    // TODO: Open edit dialog
    toast.info("Edit functionality coming soon");
    setIsOpen(false);
  };

  const handleDelete = () => {
    if (usageCount > 0) {
      toast.error(`Cannot delete item "${name}". It is used in ${usageCount} transaction(s).`);
      return;
    }
    // TODO: Open delete confirmation dialog
    toast.info("Delete functionality coming soon");
    setIsOpen(false);
  };

  const displayName = payee ? `${name} (${payee})` : name;

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={handleView}>
          <Eye className="mr-2 h-4 w-4" />
          View Transactions
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleEdit}>
          <Edit className="mr-2 h-4 w-4" />
          Edit Item
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          onClick={handleDelete}
          className="text-destructive focus:text-destructive"
          disabled={usageCount > 0}
        >
          <Trash className="mr-2 h-4 w-4" />
          Delete Item
          {usageCount > 0 && (
            <span className="ml-2 text-xs text-muted-foreground">
              ({usageCount} used)
            </span>
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};