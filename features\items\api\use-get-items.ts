"use client";
import { useQuery } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/utils";

export const useGetItems = (params?: {
  search?: string;
  categoryId?: string;
  limit?: number;
  offset?: number;
}) => {
  const userId = useCurrentUserId();

  const query = useQuery({
    enabled: !!userId,
    queryKey: ["items", { userId, ...params }],
    queryFn: async () => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      const response = await client.api.items.$get(
        {
          query: {
            search: params?.search || "",
            categoryId: params?.categoryId || "",
            limit: params?.limit?.toString() || "50",
            offset: params?.offset?.toString() || "0",
          },
        },
        {
          headers: {
            "X-User-ID": userId,
          },
        },
      );

      if (!response.ok) {
        throw new Error("Failed to fetch items");
      }

      const { data } = await response.json();
      return data;
    },
  });

  return query;
};