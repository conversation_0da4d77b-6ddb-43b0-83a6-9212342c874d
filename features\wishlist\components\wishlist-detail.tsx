import { useState } from "react";
import { Plus, ExternalLink, Edit, Trash2, ShoppingBag, Star, Calendar, Target } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { formatCurrency } from "@/lib/utils";
import { WishlistPossibilityForm } from "./wishlist-possibility-form";
import { WishlistSourceForm } from "./wishlist-source-form";
import { MediaGallery } from "@/components/media-gallery";
import { useCreateWishlistPossibility } from "../api/use-create-wishlist-possibility";
import { useEditWishlistPossibility } from "../api/use-edit-wishlist-possibility";
import { useDeleteWishlistPossibility } from "../api/use-delete-wishlist-possibility";
import { useCreateWishlistSource } from "../api/use-create-wishlist-source";
import { useEditWishlistSource } from "../api/use-edit-wishlist-source";
import { useDeleteWishlistSource } from "../api/use-delete-wishlist-source";
import { format } from "date-fns";

type WishlistSource = {
  id: string;
  name: string;
  url?: string;
  price?: number;
  notes?: string;
};

type WishlistPossibility = {
  id: string;
  name: string;
  description?: string;
  notes?: string;
  sources: WishlistSource[];
};

type WishlistItem = {
  id: string;
  name: string;
  category: string;
  estimatedCost: number;
  targetAmount?: number;
  quantity: number;
  targetDate?: Date;
  priority: string;
  status: string;
  notes?: string;
  links?: string;
  imageUrl?: string;
  motivation?: string;
  possibilities: WishlistPossibility[];
};

type Props = {
  item: WishlistItem;
};

export const WishlistDetail = ({ item }: Props) => {
  const [possibilityDialogOpen, setPossibilityDialogOpen] = useState(false);
  const [sourceDialogOpen, setSourceDialogOpen] = useState(false);
  const [editingPossibility, setEditingPossibility] = useState<WishlistPossibility | null>(null);
  const [editingSource, setEditingSource] = useState<{ source: WishlistSource; possibilityId: string } | null>(null);
  const [selectedPossibilityId, setSelectedPossibilityId] = useState<string>("");

  const createPossibilityMutation = useCreateWishlistPossibility();
  const editPossibilityMutation = useEditWishlistPossibility();
  const deletePossibilityMutation = useDeleteWishlistPossibility();
  const createSourceMutation = useCreateWishlistSource();
  const editSourceMutation = useEditWishlistSource();
  const deleteSourceMutation = useDeleteWishlistSource();

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800";
      case "medium": return "bg-yellow-100 text-yellow-800";
      case "low": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "planned": return "bg-blue-100 text-blue-800";
      case "in_progress": return "bg-purple-100 text-purple-800";
      case "on_hold": return "bg-orange-100 text-orange-800";
      case "achieved": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const handleCreatePossibility = (values: any, files?: FileList) => {
    createPossibilityMutation.mutate(
      { wishlistItemId: item.id, possibility: values, files },
      {
        onSuccess: () => {
          setPossibilityDialogOpen(false);
        },
      }
    );
  };

  const handleEditPossibility = (values: any, files?: FileList) => {
    if (!editingPossibility) return;
    editPossibilityMutation.mutate(
      { id: editingPossibility.id, possibility: values, files },
      {
        onSuccess: () => {
          setEditingPossibility(null);
          setPossibilityDialogOpen(false);
        },
      }
    );
  };

  const handleDeletePossibility = () => {
    if (!editingPossibility) return;
    deletePossibilityMutation.mutate(editingPossibility.id, {
      onSuccess: () => {
        setEditingPossibility(null);
        setPossibilityDialogOpen(false);
      },
    });
  };

  const handleCreateSource = (values: any) => {
    if (!selectedPossibilityId) return;
    createSourceMutation.mutate(
      { possibilityId: selectedPossibilityId, source: values },
      {
        onSuccess: () => {
          setSourceDialogOpen(false);
          setSelectedPossibilityId("");
        },
      }
    );
  };

  const handleEditSource = (values: any) => {
    if (!editingSource) return;
    editSourceMutation.mutate(
      { id: editingSource.source.id, source: values },
      {
        onSuccess: () => {
          setEditingSource(null);
          setSourceDialogOpen(false);
        },
      }
    );
  };

  const handleDeleteSource = () => {
    if (!editingSource) return;
    deleteSourceMutation.mutate(editingSource.source.id, {
      onSuccess: () => {
        setEditingSource(null);
        setSourceDialogOpen(false);
      },
    });
  };

  return (
    <div className="space-y-10">
      {/* Hero Section */}
      <Card className="overflow-hidden border-0 shadow-2xl">
        <div className="relative bg-gradient-to-br from-violet-50 via-indigo-50 to-blue-50">
          {/* Decorative background elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-violet-500/5 via-indigo-500/5 to-blue-500/5"></div>
          <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-indigo-200/20 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-72 h-72 bg-gradient-to-tr from-violet-200/20 to-transparent rounded-full blur-2xl"></div>
          
          <div className="relative p-8 lg:p-10">
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
              {/* Main Info */}
              <div className="lg:col-span-3 space-y-6">
                {/* Header */}
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-indigo-500 to-violet-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <ShoppingBag className="h-8 w-8 text-white" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <h1 className="text-4xl font-bold text-gray-900 leading-tight">{item.name}</h1>
                    <p className="text-xl text-indigo-600 font-semibold mt-1">{item.category}</p>
                  </div>
                </div>

                {/* Status Badges */}
                <div className="flex flex-wrap gap-3">
                  <Badge className={`${getPriorityColor(item.priority)} px-4 py-2 text-sm font-semibold shadow-sm`}>
                    <Star className="h-4 w-4 mr-2" />
                    {item.priority.toUpperCase()} PRIORITY
                  </Badge>
                  <Badge className={`${getStatusColor(item.status)} px-4 py-2 text-sm font-semibold shadow-sm`}>
                    {item.status.replace("_", " ").toUpperCase()}
                  </Badge>
                  <Badge className="bg-gray-100 text-gray-800 px-4 py-2 text-sm font-semibold shadow-sm">
                    Quantity: {item.quantity}
                  </Badge>
                </div>

                {/* Motivation */}
                {item.motivation && (
                  <div className="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/50 shadow-sm">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                      <p className="text-sm font-semibold text-indigo-800 uppercase tracking-wide">Why I want this</p>
                    </div>
                    <p className="text-gray-700 text-lg italic leading-relaxed">"{item.motivation}"</p>
                  </div>
                )}

                {/* Additional Info */}
                {(item.notes || item.links) && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {item.notes && (
                      <div className="bg-white/70 backdrop-blur-sm rounded-xl p-5 border border-white/50 shadow-sm">
                        <div className="flex items-center gap-2 mb-3">
                          <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                          <p className="text-sm font-semibold text-emerald-800 uppercase tracking-wide">Notes</p>
                        </div>
                        <p className="text-gray-700 text-sm leading-relaxed">{item.notes}</p>
                      </div>
                    )}

                    {item.links && (
                      <div className="bg-white/70 backdrop-blur-sm rounded-xl p-5 border border-white/50 shadow-sm">
                        <div className="flex items-center gap-2 mb-3">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <p className="text-sm font-semibold text-blue-800 uppercase tracking-wide">Quick Link</p>
                        </div>
                        <a
                          href={item.links}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 font-semibold text-sm transition-colors duration-200 group"
                        >
                          Visit Resource 
                          <ExternalLink className="h-4 w-4 group-hover:translate-x-0.5 transition-transform duration-200" />
                        </a>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Image & Stats Sidebar */}
              <div className="lg:col-span-2 space-y-6">
                {/* Image */}
                {item.imageUrl && (
                  <div className="relative">
                    <div className="aspect-square w-full max-w-64 mx-auto relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-indigo-500 to-violet-600 rounded-2xl rotate-3"></div>
                      <img
                        src={item.imageUrl}
                        alt={item.name}
                        className="relative w-full h-full object-cover rounded-2xl shadow-2xl border-4 border-white"
                      />
                    </div>
                  </div>
                )}
                
                {/* Key Statistics */}
                <div className="space-y-4">
                  {/* Main Cost */}
                  <div className="bg-gradient-to-r from-indigo-500 to-violet-600 rounded-xl p-6 text-center text-white shadow-lg">
                    <p className="text-3xl font-bold mb-1">{formatCurrency(item.estimatedCost)}</p>
                    <p className="text-indigo-100 text-sm font-medium uppercase tracking-wide">Estimated Cost</p>
                  </div>
                  
                  {/* Additional Stats Grid */}
                  <div className="grid grid-cols-2 gap-3">
                    {item.targetAmount && (
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center border border-white/50 shadow-sm">
                        <Target className="h-5 w-5 text-emerald-600 mx-auto mb-2" />
                        <p className="text-lg font-bold text-emerald-700">{formatCurrency(item.targetAmount)}</p>
                        <p className="text-xs text-gray-600 font-medium uppercase tracking-wide">Target</p>
                      </div>
                    )}
                    
                    {item.targetDate && (
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center border border-white/50 shadow-sm">
                        <Calendar className="h-5 w-5 text-orange-600 mx-auto mb-2" />
                        <p className="text-sm font-bold text-orange-700">{format(new Date(item.targetDate), "MMM dd")}</p>
                        <p className="text-xs text-gray-600 font-medium uppercase tracking-wide">Due Date</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Possibilities Section */}
      <div className="space-y-6">
        {/* Section Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="w-14 h-14 bg-gradient-to-br from-indigo-500 to-violet-600 rounded-xl flex items-center justify-center shadow-lg">
              <ShoppingBag className="h-7 w-7 text-white" />
            </div>
            <div>
              <h2 className="text-3xl font-bold text-gray-900">Shopping Options</h2>
              <p className="text-gray-600 text-lg">Compare different possibilities and their sources</p>
            </div>
          </div>
          <Dialog open={possibilityDialogOpen} onOpenChange={setPossibilityDialogOpen}>
            <DialogTrigger asChild>
              <Button
                onClick={() => setEditingPossibility(null)}
                className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Plus className="h-5 w-5 mr-2" />
                Add New Option
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {editingPossibility ? "Edit" : "Add"} Possibility
                </DialogTitle>
              </DialogHeader>
              <WishlistPossibilityForm
                id={editingPossibility?.id}
                defaultValues={editingPossibility ? {
                  name: editingPossibility.name,
                  description: editingPossibility.description || "",
                  notes: editingPossibility.notes || "",
                } : undefined}
                onSubmit={editingPossibility ? handleEditPossibility : handleCreatePossibility}
                onDelete={editingPossibility ? handleDeletePossibility : undefined}
                disabled={createPossibilityMutation.isPending || editPossibilityMutation.isPending || deletePossibilityMutation.isPending}
              />
            </DialogContent>
          </Dialog>
        </div>

        {item.possibilities.length === 0 ? (
          <Card className="border-0 bg-gradient-to-br from-gray-50 to-gray-100 shadow-lg">
            <CardContent className="flex flex-col items-center justify-center py-16">
              <div className="w-20 h-20 bg-gradient-to-br from-gray-300 to-gray-400 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
                <ShoppingBag className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-700 mb-3">No shopping options yet</h3>
              <p className="text-gray-500 text-center text-lg mb-8 max-w-md leading-relaxed">
                Start exploring by adding different possibilities for this item. Compare sources, prices, and find the best deals.
              </p>
              <Button
                onClick={() => setPossibilityDialogOpen(true)}
                className="bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700 text-white px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 text-lg"
              >
                <Plus className="h-5 w-5 mr-3" />
                Add Your First Option
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-8">
            {item.possibilities.map((possibility, index) => (
              <Card key={possibility.id} className="overflow-hidden border-0 shadow-xl bg-white">
                {/* Card Header */}
                <div className="relative bg-gradient-to-br from-violet-50 via-indigo-50 to-blue-50 border-l-4 border-l-indigo-500">
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/5 to-violet-500/5"></div>
                  <CardHeader className="relative p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-3">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-violet-600 rounded-xl flex items-center justify-center shadow-lg">
                            <span className="text-white font-bold text-lg">{index + 1}</span>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-1">
                              <h3 className="text-2xl font-bold text-gray-900">{possibility.name}</h3>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => {
                                  setEditingPossibility(possibility);
                                  setPossibilityDialogOpen(true);
                                }}
                                className="hover:bg-white/70 rounded-lg p-2"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            </div>
                            {possibility.description && (
                              <p className="text-gray-700 text-lg leading-relaxed">{possibility.description}</p>
                            )}
                          </div>
                        </div>
                        
                        {possibility.notes && (
                          <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/50 shadow-sm">
                            <div className="flex items-center gap-2 mb-2">
                              <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                              <p className="text-sm font-semibold text-amber-800 uppercase tracking-wide">Notes</p>
                            </div>
                            <p className="text-gray-700 text-sm leading-relaxed">{possibility.notes}</p>
                          </div>
                        )}

                        {/* Media Gallery for Possibility */}
                        <div className="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/50 shadow-sm">
                          <div className="flex items-center gap-2 mb-3">
                            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                            <p className="text-sm font-semibold text-purple-800 uppercase tracking-wide">Images</p>
                          </div>
                          <MediaGallery
                            entityType="wishlist_possibility"
                            entityId={possibility.id}
                            category="images"
                            allowDelete={false}
                            showViewToggle={true}
                            compact={true}
                          />
                        </div>
                      </div>
                      
                      <Dialog open={sourceDialogOpen} onOpenChange={setSourceDialogOpen}>
                        <DialogTrigger asChild>
                          <Button
                            onClick={() => {
                              setSelectedPossibilityId(possibility.id);
                              setEditingSource(null);
                            }}
                            className="bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white px-4 py-2 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Add Source
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>
                              {editingSource ? "Edit" : "Add"} Source
                            </DialogTitle>
                          </DialogHeader>
                          <WishlistSourceForm
                            id={editingSource?.source.id}
                            defaultValues={editingSource ? {
                              name: editingSource.source.name,
                              url: editingSource.source.url || "",
                              price: editingSource.source.price?.toString() || "",
                              notes: editingSource.source.notes || "",
                            } : undefined}
                            onSubmit={editingSource ? handleEditSource : handleCreateSource}
                            onDelete={editingSource ? handleDeleteSource : undefined}
                            disabled={createSourceMutation.isPending || editSourceMutation.isPending || deleteSourceMutation.isPending}
                          />
                        </DialogContent>
                      </Dialog>
                    </div>
                  </CardHeader>
                </div>

                <CardContent className="p-6 pt-0">
                  {possibility.sources.length === 0 ? (
                    <div className="text-center py-12 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border-2 border-dashed border-gray-300">
                      <div className="w-16 h-16 bg-gradient-to-br from-gray-300 to-gray-400 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <ShoppingBag className="h-8 w-8 text-white" />
                      </div>
                      <p className="text-gray-500 text-lg mb-6">No sources added for this option</p>
                      <Button
                        onClick={() => {
                          setSelectedPossibilityId(possibility.id);
                          setEditingSource(null);
                          setSourceDialogOpen(true);
                        }}
                        className="bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
                      >
                        <Plus className="h-5 w-5 mr-2" />
                        Add First Source
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {/* Sources Header */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg flex items-center justify-center">
                            <ShoppingBag className="h-4 w-4 text-white" />
                          </div>
                          <span className="text-lg font-bold text-gray-900">Available Sources</span>
                        </div>
                        <Badge className="bg-emerald-100 text-emerald-800 px-3 py-1 font-semibold shadow-sm">
                          {possibility.sources.length} source{possibility.sources.length !== 1 ? 's' : ''}
                        </Badge>
                      </div>
                      
                      {/* Sources Grid */}
                      <div className="grid gap-4">
                        {possibility.sources.map((source) => (
                          <div key={source.id} className="bg-gradient-to-r from-white to-gray-50 border border-gray-200 rounded-xl p-5 hover:shadow-lg transition-all duration-200 hover:border-indigo-200">
                            <div className="flex items-start justify-between">
                              <div className="flex-1 space-y-3">
                                <div className="flex items-center gap-3 flex-wrap">
                                  <h4 className="font-bold text-gray-900 text-lg">{source.name}</h4>
                                  {source.price && (
                                    <Badge className="bg-emerald-100 text-emerald-800 font-bold px-3 py-1 text-sm shadow-sm">
                                      {formatCurrency(source.price)}
                                    </Badge>
                                  )}
                                </div>
                                
                                {source.url && (
                                  <a
                                    href={source.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 font-semibold text-sm transition-colors duration-200 group"
                                  >
                                    <ExternalLink className="h-4 w-4 group-hover:translate-x-0.5 transition-transform duration-200" />
                                    Visit Store
                                  </a>
                                )}
                                
                                {source.notes && (
                                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 mt-3">
                                    <div className="flex items-center gap-2 mb-1">
                                      <div className="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                                      <span className="text-xs font-semibold text-amber-800 uppercase tracking-wide">Notes</span>
                                    </div>
                                    <p className="text-sm text-gray-700 leading-relaxed">{source.notes}</p>
                                  </div>
                                )}
                              </div>
                              
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => {
                                  setEditingSource({ source, possibilityId: possibility.id });
                                  setSourceDialogOpen(true);
                                }}
                                className="hover:bg-indigo-100 rounded-lg p-2 ml-4 flex-shrink-0"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};