"use client";

import { z } from "zod";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { insertItemSchema } from "@/db/schema";
import { useCreateItem } from "../api/use-create-item";
import { ItemForm } from "./item-form";

const formSchema = insertItemSchema.omit({
  id: true,
  userId: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  tagIds: z.array(z.string()).optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface NewItemDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const NewItemDialog = ({
  isOpen,
  onClose,
}: NewItemDialogProps) => {
  const createMutation = useCreateItem();

  const isPending = createMutation.isPending;

  const onSubmit = (values: FormValues) => {
    createMutation.mutate(values, {
      onSuccess: () => {
        onClose();
      },
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Create Item</DialogTitle>
          <DialogDescription>
            Add a new item to your inventory. You can add details and tags to organize it better.
          </DialogDescription>
        </DialogHeader>
        
        <ItemForm
          onSubmit={onSubmit}
          onCancel={onClose}
          disabled={isPending}
        />
      </DialogContent>
    </Dialog>
  );
};