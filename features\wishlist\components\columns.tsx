"use client";

import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, MoreHorizontal, Edit, Trash, Eye, ChevronDown, ChevronUp, Image } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { useEditWishlistItem } from "@/features/wishlist/hooks/use-edit-wishlist-item";
import { useDeleteWishlistItem } from "@/features/wishlist/api/use-delete-wishlist-item";
import { convertAmountFormMiliunits } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { MediaTablePreview } from "@/components/media-table-preview";

export type WishlistItem = {
  id: string;
  name: string;
  category: string;
  estimatedCost: number;
  targetAmount?: number;
  quantity: number;
  targetDate?: Date;
  priority: string;
  status: string;
  notes?: string;
  links?: string;
  imageUrl?: string;
  motivation?: string;
  possibilities?: Array<{
    id: string;
    name: string;
    description?: string;
    notes?: string;
    sources: Array<{
      id: string;
      name: string;
      url?: string;
      price?: number;
      notes?: string;
    }>;
  }>;
  createdAt: Date;
  updatedAt: Date;
};

const ActionsCell = ({ id }: { id: string }) => {
  const { onOpen } = useEditWishlistItem();
  const deleteMutation = useDeleteWishlistItem(id);
  const router = useRouter();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => router.push(`/dashboard/wishlist/${id}`)}>
          <Eye className="mr-2 h-4 w-4" />
          View Details
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onOpen(id)}>
          <Edit className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => deleteMutation.mutate()}
          disabled={deleteMutation.isPending}
        >
          <Trash className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const StatusBadge = ({ status }: { status: string }) => {
  const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
    planned: "outline",
    in_progress: "default",
    on_hold: "secondary",
    achieved: "default",
  };

  const colors: Record<string, string> = {
    planned: "text-blue-600 bg-blue-50 border-blue-200",
    in_progress: "text-yellow-600 bg-yellow-50 border-yellow-200",
    on_hold: "text-gray-600 bg-gray-50 border-gray-200",
    achieved: "text-green-600 bg-green-50 border-green-200",
  };

  return (
    <Badge variant={variants[status] || "outline"} className={colors[status]}>
      {status.replace("_", " ").toUpperCase()}
    </Badge>
  );
};

const PriorityBadge = ({ priority }: { priority: string }) => {
  const colors: Record<string, string> = {
    high: "text-red-600 bg-red-50 border-red-200",
    medium: "text-yellow-600 bg-yellow-50 border-yellow-200",
    low: "text-green-600 bg-green-50 border-green-200",
  };

  return (
    <Badge variant="outline" className={colors[priority]}>
      {priority.toUpperCase()}
    </Badge>
  );
};

export const columns: ColumnDef<WishlistItem>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const name = row.getValue("name") as string;
      const imageUrl = row.original.imageUrl;
      const hasDetails = row.original.possibilities && row.original.possibilities.length > 0;
      
      return (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {imageUrl && (
              <img 
                src={imageUrl} 
                alt={name}
                className="w-8 h-8 rounded object-cover"
              />
            )}
            <span className="font-medium">{name}</span>
          </div>
          {hasDetails && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => row.toggleExpanded()}
              className="text-xs hover:bg-none hover:text-indigo-600 text-indigo-600"
            >
              {row.getIsExpanded() ? (
                <>
                  Hide <ChevronUp className="ml-1 h-3 w-3" />
                </>
              ) : (
                <>
                  Details <ChevronDown className="ml-1 h-3 w-3" />
                </>
              )}
            </Button>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "category",
    header: "Category",
    cell: ({ row }) => {
      const category = row.getValue("category") as string;
      return <Badge variant="secondary">{category}</Badge>;
    },
  },
  {
    accessorKey: "estimatedCost",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Estimated Cost
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const amount = row.getValue("estimatedCost") as number;
      return <span className="font-medium">${convertAmountFormMiliunits(amount)}</span>;
    },
  },
  {
    accessorKey: "quantity",
    header: "Qty",
    cell: ({ row }) => {
      const quantity = row.getValue("quantity") as number;
      return <span className="font-medium text-center w-12">{quantity}</span>;
    },
  },
  {
    accessorKey: "priority",
    header: "Priority",
    cell: ({ row }) => {
      const priority = row.getValue("priority") as string;
      return <PriorityBadge priority={priority} />;
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;
      return <StatusBadge status={status} />;
    },
  },
  {
    accessorKey: "targetDate",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Target Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = row.getValue("targetDate") as Date;
      return date ? format(new Date(date), "MMM dd, yyyy") : "-";
    },
  },
  {
    accessorKey: "motivation",
    header: "Motivation",
    cell: ({ row }) => {
      const motivation = row.getValue("motivation") as string;
      return motivation ? (
        <span className="text-sm text-muted-foreground max-w-[200px] truncate">
          {motivation}
        </span>
      ) : "-";
    },
  },
  {
    id: "media",
    header: () => {
      return (
        <div className="flex items-center gap-2">
          <Image className="h-4 w-4" />
          Media
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <MediaTablePreview
          entityType="wishlist"
          entityId={row.original.id}
          category="images"
          compact={true}
        />
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <ActionsCell id={row.original.id} />,
  },
];