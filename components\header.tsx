"use client";

import { <PERSON><PERSON><PERSON><PERSON> } from "./header-logo";
import { Navigation } from "./navigation";
import { UserButton } from "./user-button";
import { WelcomeMsg } from "./welcoe-msg";
import { Filter } from "./filter";
import { ReceiptTransactionCreator } from "./receipt-transaction-creator";

import WeekResult from "./week-resume";

import Carousel from "./caroussel";
import GoalOverview from "./goal-overview";
import { Button } from "@/components/ui/button";
import { Camera, Upload } from "lucide-react";
import { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export const Header = () => {
  const [isReceiptDialogOpen, setIsReceiptDialogOpen] = useState(false);

  return (
    <header className="relative  bg-gradient-to-br md:bg-gradient-to-r from-indigo-700 from-10% via-sky-500 via-50% to-emerald-500 to-90% px-8 lg:px-16 py-8 pb-36">
      <div className="max-w-screen-2xl mx-auto">
        <div className="w-full  flex items-center justify-between mb-14">
          <div className="flex items-center  justify-end lg:gap-x-32 ">
            <HeaderLogo />
            <Navigation />
          </div>
          <div className="flex items-center gap-3">
            <Dialog open={isReceiptDialogOpen} onOpenChange={setIsReceiptDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="secondary"
                  size="sm"
                  className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                  title="Upload Receipt"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Receipt
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <Camera className="h-5 w-5" />
                    Create Transactions from Receipt
                  </DialogTitle>
                  <DialogDescription>
                    Upload receipt photos or files to automatically extract transaction data
                  </DialogDescription>
                </DialogHeader>
                <ReceiptTransactionCreator />
              </DialogContent>
            </Dialog>
            <UserButton />
          </div>
        </div>
        <div className="flex w-full flex-col  gap-8 lg:gap-2 lg:flex-row items-stretch sm:justify-center justify-between ">
          <div className="w-full lg:flex-1 items-stretch justify-stretch">
            <WelcomeMsg />
            <Filter />
          </div>
          <div className=" w-full lg:flex-1 flex justify-center lg:justify-end mr-5">
            <div className="w-full lg:-mt-12 md:max-w-[500px] ">
              <Carousel
                elements={[
                  { jsx: <GoalOverview />, title: "Spending Overview" },
                  /*  { jsx: <WeekResult />, title: "Weekly Review" }, */
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* { chatOpen && <Chat /> } */}
    </header>
  );
};
