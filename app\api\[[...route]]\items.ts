import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { getServerUserId } from "@/lib/utils";
import { db } from "@/db/drizzle";
import { items, categories, itemTransactions, itemTags, tags } from "@/db/schema";
import { insertItemSchema } from "@/db/schema";
import { createId } from "@paralleldrive/cuid2";
import { and, eq, ilike, sql, inArray } from "drizzle-orm";

const app = new Hono()
  .get(
    "/",
    zValidator(
      "query",
      z.object({
        search: z.string().optional(),
        categoryId: z.string().optional(),
        tagIds: z.string().optional(), // Comma-separated tag IDs
        limit: z.coerce.number().optional().default(50),
        offset: z.coerce.number().optional().default(0),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { search, categoryId, tagIds, limit, offset } = c.req.valid("query");

        // Build conditions array
        let conditions = [eq(items.userId, userId)];

        if (search) {
          conditions.push(ilike(items.name, `%${search}%`));
        }

        if (categoryId) {
          conditions.push(eq(items.defaultCategoryId, categoryId));
        }

        // Handle tag filtering
        if (tagIds) {
          const tagIdArray = tagIds.split(',').filter(id => id.trim());
          if (tagIdArray.length > 0) {
            // Find items that have ALL of the specified tags
            const itemsWithTags = db
              .select({ itemId: itemTags.itemId })
              .from(itemTags)
              .where(inArray(itemTags.tagId, tagIdArray))
              .groupBy(itemTags.itemId)
              .having(sql`count(*) = ${tagIdArray.length}`);

            conditions.push(sql`${items.id} IN (${itemsWithTags})`);
          }
        }

        // Build the complete query with all conditions
        const query = db
          .select({
            id: items.id,
            name: items.name,
            description: items.description,
            payee: items.payee,
            defaultCategoryId: items.defaultCategoryId,
            defaultCategoryName: categories.name,
            barcode: items.barcode,
            usageCount: sql<number>`count(DISTINCT ${itemTransactions.id})`.as("usageCount"),
            tags: sql<string>`STRING_AGG(${tags.id} || ':' || ${tags.name} || ':' || COALESCE(${tags.color}, ''), ',' ORDER BY ${tags.name})`.as("tags"),
            createdAt: items.createdAt,
            updatedAt: items.updatedAt,
          })
          .from(items)
          .leftJoin(categories, eq(items.defaultCategoryId, categories.id))
          .leftJoin(itemTransactions, eq(items.id, itemTransactions.itemId))
          .leftJoin(itemTags, eq(items.id, itemTags.itemId))
          .leftJoin(tags, eq(itemTags.tagId, tags.id))
          .where(and(...conditions))
          .groupBy(
            items.id,
            items.name,
            items.description,
            items.payee,
            items.defaultCategoryId,
            categories.name,
            items.barcode,
            items.createdAt,
            items.updatedAt
          )
          .limit(limit)
          .offset(offset);

        const rawData = await query;

        // Transform the data to include parsed tags
        const data = rawData.map((item: any) => ({
          ...item,
          tags: item.tags ? item.tags.split(',').map((tagStr: string) => {
            const [id, name, color] = tagStr.split(':');
            return { id, name, color: color || null };
          }).filter((tag: any) => tag.id) : []
        }));

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .get(
    "/:id",
    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        const [rawData] = await db
          .select({
            id: items.id,
            name: items.name,
            description: items.description,
            payee: items.payee,
            defaultCategoryId: items.defaultCategoryId,
            defaultCategoryName: categories.name,
            barcode: items.barcode,
            usageCount: sql<number>`count(DISTINCT ${itemTransactions.id})`.as("usageCount"),
            tags: sql<string>`STRING_AGG(${tags.id} || ':' || ${tags.name} || ':' || COALESCE(${tags.color}, ''), ',' ORDER BY ${tags.name})`.as("tags"),
            createdAt: items.createdAt,
            updatedAt: items.updatedAt,
          })
          .from(items)
          .leftJoin(categories, eq(items.defaultCategoryId, categories.id))
          .leftJoin(itemTransactions, eq(items.id, itemTransactions.itemId))
          .leftJoin(itemTags, eq(items.id, itemTags.itemId))
          .leftJoin(tags, eq(itemTags.tagId, tags.id))
          .where(and(eq(items.id, id), eq(items.userId, userId)))
          .groupBy(
            items.id,
            items.name,
            items.description,
            items.payee,
            items.defaultCategoryId,
            categories.name,
            items.barcode,
            items.createdAt,
            items.updatedAt
          );

        if (!rawData) {
          return c.json({ error: "Not found" }, 404);
        }

        // Transform the data to include parsed tags
        const data = {
          ...rawData,
          tags: rawData.tags ? rawData.tags.split(',').map(tagStr => {
            const [id, name, color] = tagStr.split(':');
            return { id, name, color: color || null };
          }).filter(tag => tag.id) : []
        };

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .get(
    "/search",
    zValidator(
      "query",
      z.object({
        name: z.string().min(1),
        exact: z.boolean().optional().default(false),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { name, exact } = c.req.valid("query");

        const searchCondition = exact 
          ? eq(sql`TRIM(LOWER(${items.name}))`, name.trim().toLowerCase())
          : ilike(items.name, `%${name}%`);

        const data = await db
          .select({
            id: items.id,
            name: items.name,
            description: items.description,
            payee: items.payee,
            defaultCategoryId: items.defaultCategoryId,
            defaultCategoryName: categories.name,
            barcode: items.barcode,
          })
          .from(items)
          .leftJoin(categories, eq(items.defaultCategoryId, categories.id))
          .where(
            and(
              eq(items.userId, userId),
              searchCondition
            )
          )
          .limit(10);

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post(
    "/",
    zValidator("json", insertItemSchema.omit({ id: true, createdAt: true, updatedAt: true }).extend({
      tagIds: z.array(z.string()).optional(),
    })),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { tagIds, ...values } = c.req.valid("json");

        // Check if item with same name already exists for this user
        const existingItem = await db
          .select()
          .from(items)
          .where(
            and(
              eq(items.userId, userId),
              eq(sql`TRIM(LOWER(${items.name}))`, values.name.trim().toLowerCase())
            )
          )
          .then(rows => rows[0]);

        if (existingItem) {
          return c.json({ error: "Item with this name already exists" }, 400);
        }

        // Create the item first
        const [data] = await db
          .insert(items)
          .values({
            id: createId(),
            ...values,
            userId,
          })
          .returning();

        // Add tags if provided
        if (tagIds && tagIds.length > 0) {
          const itemTagsToInsert = tagIds.map(tagId => ({
            id: createId(),
            itemId: data.id,
            tagId,
          }));
          
          await db.insert(itemTags).values(itemTagsToInsert);
        }

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .patch(
    "/:id",
    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    zValidator("json", insertItemSchema.omit({ id: true, userId: true, createdAt: true, updatedAt: true }).extend({
      tagIds: z.array(z.string()).optional(),
    }).partial()),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");
        const { tagIds, ...values } = c.req.valid("json");

        // If updating name, check for duplicates
        if (values.name) {
          const existingItem = await db
            .select()
            .from(items)
            .where(
              and(
                eq(items.userId, userId),
                eq(sql`TRIM(LOWER(${items.name}))`, values.name.trim().toLowerCase()),
                sql`${items.id} != ${id}`
              )
            )
            .then(rows => rows[0]);

          if (existingItem) {
            return c.json({ error: "Item with this name already exists" }, 400);
          }
        }

        // Update the item
        const [data] = await db
          .update(items)
          .set({
            ...values,
            updatedAt: new Date(),
          })
          .where(and(eq(items.id, id), eq(items.userId, userId)))
          .returning();

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        // Update tags if provided
        if (tagIds !== undefined) {
          // Delete existing tags
          await db.delete(itemTags).where(eq(itemTags.itemId, id));
          
          // Add new tags
          if (tagIds.length > 0) {
            const itemTagsToInsert = tagIds.map(tagId => ({
              id: createId(),
              itemId: id,
              tagId,
            }));
            
            await db.insert(itemTags).values(itemTagsToInsert);
          }
        }

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .delete(
    "/:id",
    zValidator(
      "param",
      z.object({
        id: z.string(),
      }),
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { id } = c.req.valid("param");

        // Check if item is used in any transactions
        const usageCount = await db
          .select({ count: sql<number>`count(*)` })
          .from(itemTransactions)
          .where(eq(itemTransactions.itemId, id))
          .then(rows => rows[0]?.count || 0);

        if (usageCount > 0) {
          return c.json({ 
            error: `Cannot delete item. It is used in ${usageCount} transaction(s).` 
          }, 400);
        }

        const [data] = await db
          .delete(items)
          .where(and(eq(items.id, id), eq(items.userId, userId)))
          .returning({
            id: items.id,
          });

        if (!data) {
          return c.json({ error: "Not found" }, 404);
        }

        return c.json({ data });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  )
  .post(
    "/find-or-create",
    zValidator(
      "json", 
      z.object({
        name: z.string().min(1),
        description: z.string().optional(),
        defaultCategoryId: z.string().optional(),
        payee: z.string().optional(),
        tagIds: z.array(z.string()).optional(),
      })
    ),
    async (c) => {
      try {
        const userId = await getServerUserId(c.req.raw.headers);
        const { name, description, defaultCategoryId, payee, tagIds } = c.req.valid("json");

        // Look for existing item considering both name and payee
        const whereConditions = [
          eq(items.userId, userId),
          eq(sql`TRIM(LOWER(${items.name}))`, name.trim().toLowerCase())
        ];

        // If payee is provided, include it in the search to differentiate items from different stores/vendors
        if (payee && payee.trim()) {
          whereConditions.push(eq(sql`TRIM(LOWER(${items.payee}))`, payee.trim().toLowerCase()));
        } else {
          // If no payee provided, only match items that also have no payee
          whereConditions.push(sql`${items.payee} IS NULL`);
        }

        const existingItem = await db
          .select()
          .from(items)
          .where(and(...whereConditions))
          .then(rows => rows[0]);

        if (existingItem) {
          return c.json({ data: existingItem, created: false });
        }

        // Create new item if not found
        const itemId = createId();
        const [newItem] = await db
          .insert(items)
          .values({
            id: itemId,
            name: name.trim(),
            description,
            payee: payee?.trim() || null,
            defaultCategoryId,
            userId,
          })
          .returning();

        // Add tags if provided
        if (tagIds && tagIds.length > 0) {
          const itemTagsToInsert = tagIds.map(tagId => ({
            id: createId(),
            itemId: itemId,
            tagId,
          }));
          
          await db.insert(itemTags).values(itemTagsToInsert);
        }

        return c.json({ data: newItem, created: true });
      } catch (error) {
        return c.json({ error: "Unauthorized" }, 401);
      }
    },
  );

export default app;