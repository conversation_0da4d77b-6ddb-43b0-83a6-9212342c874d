"use client";

import { useGetCategories } from "@/features/categories/api/use-get-categories";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type Props = {
  categoryId: string;
  onChange: (value: string) => void;
};

export const CategoryFilter = ({ categoryId, onChange }: Props) => {
  const { data: categories, isLoading } = useGetCategories();

  return (
    <Select value={categoryId} onValueChange={onChange} disabled={isLoading}>
      <SelectTrigger className="lg:w-auto w-full h-9">
        <SelectValue placeholder="All Categories" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="">All Categories</SelectItem>
        {categories?.map((category) => (
          <SelectItem key={category.id} value={category.id}>
            {category.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
