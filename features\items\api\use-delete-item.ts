import { useMutation, useQueryClient } from "@tanstack/react-query";
import { InferResponseType } from "hono";
import { toast } from "sonner";

import { client } from "@/lib/hono";
import { useCurrentUserId } from "@/lib/auth-client";

type ResponseType = InferResponseType<typeof client.api.items[":id"]["$delete"]>;

export const useDeleteItem = (id?: string) => {
  const queryClient = useQueryClient();
  const userId = useCurrentUserId();

  const mutation = useMutation<ResponseType, Error>({
    mutationFn: async () => {
      const response = await client.api.items[":id"]["$delete"](
        { param: { id } },
        { headers: { "X-User-ID": userId! } }
      );
      return await response.json();
    },
    onSuccess: () => {
      toast.success("Item deleted");
      queryClient.invalidateQueries({ queryKey: ["items"] });
      queryClient.invalidateQueries({ queryKey: ["item-transactions"] });
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to delete item");
    },
  });

  return mutation;
};